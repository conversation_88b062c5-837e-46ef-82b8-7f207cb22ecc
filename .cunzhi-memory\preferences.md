# 用户偏好设置

- 用户反馈IR3-Tech-Innovation组件的tech-data卡片动画在开始时有卡顿，需要优化为更流畅的Apple风格动画效果
- 用户反馈IR3-Tech-Innovation组件的tech-data动画触发时机太晚，希望在滚动到组件顶部时就立即出现动画，而不是需要继续向下滚动才触发
- 用户反馈IR3-Tech-Innovation组件的tech-data动画触发时机太早，导致看不到动画效果，需要调整触发时机
- 用户要求不要生成总结性Markdown文档、不要生成测试脚本、不要编译(用户自己编译)，但要求帮助运行
- 用户工作原则：不要生成总结性Markdown文档，不要生成测试脚本，不要编译（用户自己编译），但要帮助运行
- 用户偏好：1. 需要生成总结性Markdown文档 2. 不要生成测试脚本 3. 不要编译，用户自己编译 4. 需要帮助运行
- 用户偏好：❌不要生成总结性Markdown文档 ❌不要生成测试脚本 ❌不要编译，用户自己编译 ✔️帮我运行
- 用户要求IR3-Parameters-Display组件布局调整：标题独占一行在最上面并居中，导航在标题下方占一行，内容在导航下方占一行，采用垂直布局而非左右分栏

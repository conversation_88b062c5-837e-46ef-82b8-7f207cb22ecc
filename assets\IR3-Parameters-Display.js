/**
 * IR3 Parameters Display JavaScript
 * Handles category switching and interactive functionality
 */

class IR3ParametersDisplay {
  constructor() {
    this.init();
  }

  init() {
    this.bindEvents();
    this.initializeAnimations();
  }

  bindEvents() {
    // Category button click handlers
    const categoryButtons = document.querySelectorAll('.category-btn');
    const parameterCategories = document.querySelectorAll('.parameter-category');

    categoryButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        const targetCategory = button.getAttribute('data-category');
        this.switchCategory(targetCategory, categoryButtons, parameterCategories);
      });
    });

    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
      if (e.target.closest('.parameters-section')) {
        this.handleKeyboardNavigation(e, categoryButtons);
      }
    });

    // Parameter card hover effects
    this.initializeCardEffects();
  }

  switchCategory(targetCategory, buttons, categories) {
    // Remove active class from all buttons and categories
    buttons.forEach(btn => btn.classList.remove('active'));
    categories.forEach(cat => cat.classList.remove('active'));

    // Add active class to target button and category
    const targetButton = document.querySelector(`[data-category="${targetCategory}"]`);
    const targetCategoryElement = document.querySelector(`.parameter-category[data-category="${targetCategory}"]`);

    if (targetButton && targetCategoryElement) {
      targetButton.classList.add('active');
      targetCategoryElement.classList.add('active');

      // Trigger animation
      this.animateCategory(targetCategoryElement);

      // Update URL hash for deep linking
      this.updateUrlHash(targetCategory);

      // Analytics tracking
      this.trackCategorySwitch(targetCategory);
    }
  }

  handleKeyboardNavigation(e, buttons) {
    const activeButton = document.querySelector('.category-btn.active');
    const currentIndex = Array.from(buttons).indexOf(activeButton);
    let newIndex = currentIndex;

    switch (e.key) {
      case 'ArrowLeft':
        e.preventDefault();
        newIndex = currentIndex > 0 ? currentIndex - 1 : buttons.length - 1;
        break;
      case 'ArrowRight':
        e.preventDefault();
        newIndex = currentIndex < buttons.length - 1 ? currentIndex + 1 : 0;
        break;
      case 'Home':
        e.preventDefault();
        newIndex = 0;
        break;
      case 'End':
        e.preventDefault();
        newIndex = buttons.length - 1;
        break;
      default:
        return;
    }

    if (newIndex !== currentIndex) {
      buttons[newIndex].click();
      buttons[newIndex].focus();
    }
  }

  initializeCardEffects() {
    const cards = document.querySelectorAll('.parameter-card, .sensor-card');
    
    cards.forEach(card => {
      // Add intersection observer for scroll animations
      this.observeCard(card);

      // Add mouse move effect for subtle parallax
      card.addEventListener('mousemove', (e) => {
        this.handleCardMouseMove(e, card);
      });

      card.addEventListener('mouseleave', () => {
        this.resetCardTransform(card);
      });
    });
  }

  observeCard(card) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.animationDelay = `${Math.random() * 0.3}s`;
          entry.target.classList.add('animate-in');
        }
      });
    }, {
      threshold: 0.1,
      rootMargin: '50px'
    });

    observer.observe(card);
  }

  handleCardMouseMove(e, card) {
    const rect = card.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    const centerX = rect.width / 2;
    const centerY = rect.height / 2;
    
    const rotateX = (y - centerY) / 20;
    const rotateY = (centerX - x) / 20;
    
    card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
  }

  resetCardTransform(card) {
    card.style.transform = '';
  }

  animateCategory(categoryElement) {
    // Reset animation
    categoryElement.style.animation = 'none';
    categoryElement.offsetHeight; // Trigger reflow
    categoryElement.style.animation = 'fadeInUp 0.6s ease-out';

    // Stagger card animations
    const cards = categoryElement.querySelectorAll('.parameter-card, .sensor-card');
    cards.forEach((card, index) => {
      card.style.animationDelay = `${index * 0.1}s`;
      card.classList.add('animate-in');
    });
  }

  updateUrlHash(category) {
    if (history.replaceState) {
      history.replaceState(null, null, `#${category}`);
    }
  }

  trackCategorySwitch(category) {
    // Google Analytics tracking
    if (typeof gtag !== 'undefined') {
      gtag('event', 'category_switch', {
        event_category: 'IR3_Parameters',
        event_label: category,
        value: 1
      });
    }

    // Custom analytics
    if (typeof window.customAnalytics !== 'undefined') {
      window.customAnalytics.track('parameters_category_viewed', {
        category: category,
        timestamp: new Date().toISOString()
      });
    }
  }

  initializeAnimations() {
    // Add CSS for card animations
    const style = document.createElement('style');
    style.textContent = `
      .parameter-card.animate-in,
      .sensor-card.animate-in {
        animation: cardSlideIn 0.6s ease-out forwards;
      }
      
      @keyframes cardSlideIn {
        from {
          opacity: 0;
          transform: translateY(30px) scale(0.95);
        }
        to {
          opacity: 1;
          transform: translateY(0) scale(1);
        }
      }
    `;
    document.head.appendChild(style);
  }

  // Initialize from URL hash
  initializeFromHash() {
    const hash = window.location.hash.substring(1);
    const validCategories = ['machine', 'sensors', 'electrical', 'software'];
    
    if (hash && validCategories.includes(hash)) {
      const targetButton = document.querySelector(`[data-category="${hash}"]`);
      if (targetButton) {
        targetButton.click();
      }
    }
  }

  // Public method to switch to specific category
  switchToCategory(category) {
    const button = document.querySelector(`[data-category="${category}"]`);
    if (button) {
      button.click();
    }
  }

  // Public method to get current active category
  getCurrentCategory() {
    const activeButton = document.querySelector('.category-btn.active');
    return activeButton ? activeButton.getAttribute('data-category') : null;
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  const parametersDisplay = new IR3ParametersDisplay();
  
  // Initialize from URL hash after a short delay
  setTimeout(() => {
    parametersDisplay.initializeFromHash();
  }, 100);

  // Make instance globally available
  window.IR3ParametersDisplay = parametersDisplay;
});

// Handle hash changes
window.addEventListener('hashchange', () => {
  if (window.IR3ParametersDisplay) {
    window.IR3ParametersDisplay.initializeFromHash();
  }
});
